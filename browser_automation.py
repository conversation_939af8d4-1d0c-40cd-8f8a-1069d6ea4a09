import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import WebDriverException
from typing import Optional


class BrowserAutomation:
    """
    本地浏览器自动化控制类
    使用Selenium WebDriver控制本地Chrome浏览器
    """

    def __init__(self, chromedriver_path: Optional[str] = None, headless: bool = False):
        """
        初始化浏览器自动化控制器
        
        Args:
            chromedriver_path: ChromeDriver可执行文件路径，None则使用系统PATH
            headless: 是否以无头模式运行浏览器
        """
        self.chromedriver_path = chromedriver_path
        self.headless = headless
        self.driver = None

    def start_browser(self) -> bool:
        """
        启动本地Chrome浏览器
        
        Returns:
            启动成功返回True，失败返回False
        """
        try:
            print("\n🚗 [浏览器] 正在启动本地Chrome浏览器...")
            
            # 配置Chrome选项
            chrome_options = ChromeOptions()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            if self.headless:
                chrome_options.add_argument("--headless")
                print("ℹ️  [浏览器] 使用无头模式启动")

            # 创建ChromeDriver服务
            if self.chromedriver_path:
                service = ChromeService(executable_path=self.chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
            
            # 设置反检测
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ [浏览器] 本地Chrome浏览器启动成功！")
            return True
            
        except WebDriverException as e:
            print(f"❌ [浏览器] WebDriver错误: {e}")
            print("   - 👉 请确保已安装Chrome浏览器")
            print("   - 👉 请确保ChromeDriver版本与Chrome浏览器版本匹配")
            print("   - 👉 请确保ChromeDriver在系统PATH中或指定正确路径")
            return False
        except Exception as e:
            print(f"❌ [浏览器] 启动浏览器时发生未知错误: {e}")
            return False

    def navigate_to_url(self, url: str) -> bool:
        """
        导航到指定URL
        
        Args:
            url: 目标URL
            
        Returns:
            导航成功返回True，失败返回False
        """
        if not self.driver:
            print("❌ [浏览器] 浏览器未启动，无法导航")
            return False
            
        try:
            print(f"\n🤖 [自动化] 正在访问: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            print("⏳ [自动化] 等待页面加载...")
            time.sleep(10)
            
            print(f"   - 当前页面标题: {self.driver.title}")
            print(f"   - 当前页面URL: {self.driver.current_url}")
            return True
            
        except Exception as e:
            print(f"❌ [自动化] 导航到URL时发生错误: {e}")
            return False

    def handle_captcha(self, captcha_xpath: str = '//*[@id="RInW4"]/div/label/input', 
                      timeout: int = 60) -> bool:
        """
        处理人机验证
        
        Args:
            captcha_xpath: 验证码元素的XPath
            timeout: 等待超时时间（秒）
            
        Returns:
            处理成功返回True，失败返回False
        """
        if not self.driver:
            print("❌ [浏览器] 浏览器未启动，无法处理验证码")
            return False
            
        print("🔍 [自动化] 正在检测人机验证元素...")
        captcha_element = None
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                captcha_elements = self.driver.find_elements(By.XPATH, captcha_xpath)
                
                if len(captcha_elements) > 0:
                    captcha_element = captcha_elements[0]
                    print("✅ [自动化] 检测到验证码元素")
                    break
                else:
                    print("⏳ [自动化] 未找到验证码元素，继续等待...")
                    time.sleep(2)
                    
            except Exception as e:
                print(f"⚠️ [自动化] 检测过程中出现错误: {str(e)[:100]}...")
                time.sleep(2)

        if captcha_element:
            try:
                # 执行Tab键和空格键操作
                print("⌨️  [自动化] 执行Tab键...")
                captcha_element.send_keys(Keys.TAB)
                time.sleep(1)

                print("⌨️  [自动化] 执行空格键...")
                captcha_element.send_keys(Keys.SPACE)

                print("⏳ [自动化] 等待验证完成...")
                time.sleep(10)
                
                print("✅ [自动化] 人机验证处理完成")
                return True
                
            except Exception as e:
                print(f"⚠️ [自动化] 人机验证处理过程中出现错误: {e}")
                return False
        else:
            print("⏰ [自动化] 超时未检测到验证码元素")
            return False

    def get_page_info(self) -> dict:
        """
        获取当前页面信息
        
        Returns:
            包含页面信息的字典
        """
        if not self.driver:
            return {"error": "浏览器未启动"}
            
        try:
            return {
                "title": self.driver.title,
                "url": self.driver.current_url,
                "page_source": self.driver.page_source,
                "page_source_length": len(self.driver.page_source)
            }
        except Exception as e:
            return {"error": f"获取页面信息失败: {e}"}

    def close_browser(self):
        """
        关闭浏览器并清理资源
        """
        if self.driver:
            print("🧹 [清理] 正在关闭浏览器...")
            try:
                self.driver.quit()
                self.driver = None
                print("✅ [清理] 浏览器已关闭")
            except Exception as e:
                print(f"❌ [清理] 关闭浏览器时出错: {e}")
        else:
            print("ℹ️  [清理] 浏览器未启动，无需关闭")
