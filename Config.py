# 配置信息
class Config:
    """
    本地浏览器自动化工具配置类
    管理浏览器启动参数和外部服务配置
    """

    # === 外部服务配置 ===
    # 外部服务用户TOKEN，用于获取授权链接
    USER_TOKEN: str = "aug_a108a68ba6f9bfda"  # 请替换为实际的用户TOKEN
    # 外部服务地址
    EXTERNAL_SERVICE_URL: str = "https://augmenttoken.159email.shop"

    # === 本地浏览器配置 ===
    # ChromeDriver可执行文件路径，None则使用系统PATH中的chromedriver
    CHROMEDRIVER_PATH: str = None  # 例如: "C:/chromedriver/chromedriver.exe"

    # 是否以无头模式启动浏览器（不显示浏览器窗口）
    HEADLESS_MODE: bool = False

    # 浏览器启动等待时间（秒）
    BROWSER_STARTUP_WAIT: int = 5

    # 页面加载等待时间（秒）
    PAGE_LOAD_WAIT: int = 10

    # === 人机验证配置 ===
    # 验证码元素XPath
    CAPTCHA_XPATH: str = '//*[@id="RInW4"]/div/label/input'

    # 验证码检测超时时间（秒）
    CAPTCHA_TIMEOUT: int = 60

    # === 调试配置 ===
    # 是否启用调试模式（输出详细日志）
    DEBUG_MODE: bool = True

    # 是否输出页面HTML源码（用于调试）
    OUTPUT_PAGE_SOURCE: bool = True