# 配置信息
class Config:
    """
    脚本配置类，将所有可变配置项集中在此处管理。
    """
    # 在 MoreLogin 客户端的 "账号设置" -> "API" 中找到你的 Token
    API_TOKEN: str = "c6c6753a4ded47f7979a5b877b9dbd11"
    # 你想要启动的浏览器配置文件的 ID
    # 可以在 MoreLogin 客户端的浏览器配置文件列表中找到
    PROFILE_ID: str = "1652635259345205"
    # MoreLogin 客户端 API 的基础 URL
    # 通常是本地运行的，所以地址是 localhost
    BASE_URL: str = "http://127.0.0.1:40000"
    
    # 外部服务用户TOKEN，用于获取授权链接
    USER_TOKEN: str = "aug_a108a68ba6f9bfda"  # 请替换为实际的用户TOKEN
    # 外部服务地址
    EXTERNAL_SERVICE_URL: str = "https://augmenttoken.159email.shop"
    
    # 新增环境相关配置
    # 环境 ID，如果设置了ENV_ID将优先使用环境启动方式
    ENV_ID: str = "1953003773565149184"  # 请替换为实际的环境ID
    # 密钥，环境开启端对端加密时需要设置
    ENCRYPT_KEY: str = ""
    # 是否以 headless 方式启动浏览器环境（需要MoreLogin V2.36.0及以上版本）
    IS_HEADLESS: bool = False
    # 是否启用CDP特征规避机制（需要MoreLogin V2.36.0及以上版本）
    CDP_EVASION: bool = False
    
    # 指纹刷新配置
    BROWSER_TYPE: int = 1  # 1:Chrome, 2:Firefox
    OS_TYPE: int = 1       # 1:Windows, 2:macOS, 3:Android, 4:IOS
    UA_VERSION: int = 137  # UA版本号，根据环境配置
    
    # 本地缓存清除配置
    CLEAR_LOCAL_STORAGE: bool = True
    CLEAR_INDEXED_DB: bool = False
    CLEAR_COOKIE: bool = False
    CLEAR_EXTENSION: bool = False
    CLEAR_EXTENSION_FILE: bool = False
    
    # 是否使用 MoreLogin，测试环境设为 False，生产环境设为 True
    USE_MORELOGIN: bool = False