---
type: "always_apply"
---

# AutoBrowser 项目规则配置文件

## 项目概述

**项目名称**: AutoBrowser
**项目类型**: 本地浏览器自动化工具
**主要功能**: 基于Selenium的本地浏览器自动化系统，专门用于处理OAuth授权流程和人机验证
**技术栈**: Python 3.11+, Selenium WebDriver, Chrome浏览器, Requests
**开发语言**: Python（中文注释和文档）

### 核心功能模块

1. **外部服务集成** (`external_service.py`)
   - OAuth授权链接获取
   - 外部API调用和认证
   - HTTP请求处理

2. **浏览器自动化** (`browser_automation.py`)
   - 本地Chrome浏览器启动和控制
   - 人机验证自动处理
   - 页面元素检测和操作
   - 反自动化检测机制

3. **主程序控制** (`main.py`)
   - 程序流程控制
   - 模块协调和错误处理
   - 用户交互和状态反馈

4. **配置管理** (`Config.py`)
   - 外部服务配置
   - 本地浏览器参数设置
   - 自动化行为配置

## 代码风格和开发规范

### 1. 命名规范
- **类名**: 使用PascalCase (如: `MoreLoginAPI`, `Config`)
- **函数名**: 使用snake_case (如: `run_automation_task`, `start_profile`)
- **变量名**: 使用snake_case (如: `driver_path`, `debugger_address`)
- **常量**: 使用UPPER_SNAKE_CASE (如: `API_TOKEN`, `BASE_URL`)

### 2. 注释和文档规范
- **函数文档**: 使用中文docstring，包含Args和Returns说明
- **行内注释**: 使用中文，解释关键逻辑和业务流程
- **模块注释**: 在文件顶部用中文说明模块功能

### 3. 错误处理规范
- 使用try-except结构处理API调用和WebDriver操作
- 错误信息使用中文，包含emoji图标增强可读性
- 提供具体的错误解决建议

### 4. 日志输出规范
- 使用emoji图标标识不同类型的消息：
  - 🚀 启动操作
  - ✅ 成功操作
  - ❌ 错误信息
  - ⚠️ 警告信息
  - ℹ️ 信息提示
  - 🔄 处理中
  - ⏳ 等待状态

## 提示词优化规则

### 1. 中文提示词优化最佳实践

#### 功能描述优化
- **原则**: 使用准确的技术术语，避免模糊表达
- **示例**:
  - ❌ "处理验证码"
  - ✅ "检测并自动处理人机验证元素，通过XPath定位验证码输入框并执行Tab+Space键操作"

#### 错误信息优化
- **原则**: 提供具体的错误原因和解决方案
- **示例**:
  - ❌ "启动失败"
  - ✅ "环境启动失败: 浏览器内核未下载。请在MoreLogin客户端中为环境ID xxx下载对应的浏览器内核"

#### 配置说明优化
- **原则**: 详细说明配置项的作用和取值范围
- **示例**:
  - ❌ "浏览器类型"
  - ✅ "浏览器类型 (1:Chrome, 2:Firefox) - 用于指纹刷新时的浏览器环境配置"

### 2. API文档优化规则

#### 参数说明
- 使用中文描述参数用途
- 明确参数的数据类型和取值范围
- 提供参数的默认值和可选性说明

#### 返回值说明
- 详细描述成功和失败情况下的返回值
- 说明返回数据的结构和含义

### 3. 代码注释优化规则

#### 业务逻辑注释
- 解释为什么这样实现，而不仅仅是做了什么
- 说明特殊处理的业务背景

#### 技术实现注释
- 解释复杂算法或特殊技巧的原理
- 标注重要的技术依赖和版本要求

## 项目特定约定

### 1. 本地浏览器约定
- 使用本地Chrome浏览器进行自动化操作
- 支持指定ChromeDriver路径或使用系统PATH
- 内置反自动化检测机制

### 2. Selenium操作约定
- 直接启动本地Chrome浏览器实例
- 操作前必须等待页面充分加载（至少10秒）
- 使用XPath进行元素定位，提供备用定位策略

### 3. 错误处理约定
- API调用超时设置为30秒
- WebDriver操作包含重试机制
- 资源清理必须在finally块中执行

### 4. 配置管理约定
- 敏感信息（用户Token）不应硬编码在代码中
- 提供灵活的浏览器配置选项
- 配置验证在程序启动时进行

## 开发注意事项

### 1. 依赖管理
- 主要依赖: `selenium`, `requests`
- 确保ChromeDriver版本与Chrome浏览器版本兼容
- Python版本要求: 3.11及以上

### 2. 安全考虑
- 用户Token和外部服务凭据需要妥善保管
- 避免在日志中输出敏感信息
- 使用HTTPS进行外部API通信

### 3. 性能优化
- 合理设置等待时间，避免过度等待
- 及时释放WebDriver资源
- 使用连接池管理HTTP请求

### 4. 调试支持
- 提供详细的页面信息输出用于调试
- 支持无头模式和调试模式配置
- 包含完整的错误堆栈信息

## 团队协作规范

### 1. 代码提交规范
- 提交信息使用中文，格式: `[模块] 功能描述`
- 每次提交包含完整的功能实现
- 重要变更需要更新此规则文件

### 2. 代码审查要点
- 检查错误处理的完整性
- 验证中文注释的准确性
- 确认配置项的安全性

### 3. 测试要求
- 新功能必须包含测试用例
- 测试覆盖正常流程和异常情况
- 提供测试环境的配置说明

---

**最后更新**: 2025-08-07  
**维护者**: AutoBrowser开发团队  
**版本**: 1.0.0
