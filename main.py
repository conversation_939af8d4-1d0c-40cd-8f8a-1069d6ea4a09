import time
import sys

from Config import Config
from browser_automation import BrowserAutomation
from external_service import ExternalService


def run_automation_task(config: Config, target_url: str) -> bool:
    """
    运行本地浏览器自动化任务

    Args:
        config: 配置对象
        target_url: 要访问的目标URL

    Returns:
        任务执行成功返回True，失败返回False
    """
    # 初始化浏览器自动化控制器
    browser = BrowserAutomation(
        chromedriver_path=config.CHROMEDRIVER_PATH,
        headless=config.HEADLESS_MODE
    )

    try:
        # 启动浏览器
        if not browser.start_browser():
            print("❌ [主程序] 浏览器启动失败")
            return False

        # 等待浏览器稳定
        time.sleep(config.BROWSER_STARTUP_WAIT)

        # 导航到目标URL
        if not browser.navigate_to_url(target_url):
            print("❌ [主程序] 页面导航失败")
            return False

        # 输出页面源码（如果启用调试模式）
        if config.DEBUG_MODE and config.OUTPUT_PAGE_SOURCE:
            page_info = browser.get_page_info()
            if "error" not in page_info:
                print("📄 [调试] 当前页面信息:")
                print(f"   - 标题: {page_info['title']}")
                print(f"   - URL: {page_info['url']}")
                print(f"   - HTML长度: {page_info['page_source_length']} 字符")

        # 处理人机验证
        print("\n🔍 [主程序] 开始处理人机验证...")
        captcha_result = browser.handle_captcha(
            captcha_xpath=config.CAPTCHA_XPATH,
            timeout=config.CAPTCHA_TIMEOUT
        )

        if captcha_result:
            print("✅ [主程序] 人机验证处理成功")
        else:
            print("⚠️ [主程序] 人机验证处理失败或未找到验证码元素")

        # 获取最终页面信息
        final_info = browser.get_page_info()
        if "error" not in final_info:
            print(f"\n📋 [主程序] 最终页面信息:")
            print(f"   - 标题: {final_info['title']}")
            print(f"   - URL: {final_info['url']}")

        print("✅ [主程序] 自动化任务完成！")

        # 等待一段时间后关闭
        print("⏳ [主程序] 等待10秒后关闭浏览器...")
        time.sleep(10)

        return True

    except Exception as e:
        print(f"❌ [主程序] 执行自动化任务时发生错误: {e}")
        return False

    finally:
        # 清理资源
        browser.close_browser()


def main():
    """主执行函数"""
    print("🚀 [主程序] AutoBrowser 本地浏览器自动化工具启动")
    print("=" * 50)

    # 加载配置
    config = Config()

    # 检查必要配置
    if "YOUR_USER_TOKEN" in config.USER_TOKEN or not config.USER_TOKEN:
        print("🚨 [错误] 请先在Config.py中设置正确的USER_TOKEN")
        print("   请将USER_TOKEN设置为您的实际用户令牌")
        return False

    if not config.EXTERNAL_SERVICE_URL:
        print("🚨 [错误] 请先在Config.py中设置EXTERNAL_SERVICE_URL")
        return False

    # 初始化外部服务客户端
    print("\n🔗 [主程序] 初始化外部服务连接...")
    external_service = ExternalService(
        base_url=config.EXTERNAL_SERVICE_URL,
        user_token=config.USER_TOKEN
    )

    # 获取外部授权链接
    print("🔍 [主程序] 获取OAuth授权链接...")
    # auth_url = external_service.get_auth_url()
    # 测试地址
    auth_url = auth_url = "https://auth.augmentcode.com/authorize?response_type=code&code_challenge=xpEbeuZ1NAZRZRwWHToz7HmIC3plHkj0t5Z-hk5OnOw&client_id=v&state=7fuA1bGPoHU&prompt=login"

    if not auth_url:
        print("🚨 [错误] 无法获取外部授权链接，程序终止")
        print("   请检查：")
        print("   - USER_TOKEN是否正确")
        print("   - EXTERNAL_SERVICE_URL是否可访问")
        print("   - 网络连接是否正常")
        return False

    print(f"✅ [主程序] 成功获取授权链接: {auth_url}")

    # 执行自动化任务
    print("\n🤖 [主程序] 开始执行浏览器自动化任务...")
    success = run_automation_task(config, auth_url)

    if success:
        print("\n🎉 [主程序] 所有任务执行完成！")
        return True
    else:
        print("\n❌ [主程序] 任务执行失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 程序正常结束")
            sys.exit(0)
        else:
            print("\n❌ 程序异常结束")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序执行")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 程序发生未处理的异常: {e}")
        sys.exit(1)