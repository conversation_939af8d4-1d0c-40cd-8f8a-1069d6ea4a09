import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.common.by import By
from selenium.common.exceptions import WebDriverException

from Config import Config
from MoreLoginAPI import MoreLoginAP<PERSON>


def run_automation_task(driver_path: str, debugger_address: str, target_url: str = "https://www.baidu.com"):
    """
    使用 Selenium 连接到已启动的浏览器并执行自动化任务。

    Args:
        driver_path: WebDriver路径
        debugger_address: 调试地址
        target_url: 要访问的目标URL，默认为百度
    """
    driver = None
    try:
        print("\n🚗 [Selenium] 正在连接到浏览器...")
        chrome_options = ChromeOptions()
        # 关键步骤：通过 debuggerAddress 连接到 MoreLogin 打开的浏览器实例
        chrome_options.add_experimental_option("debuggerAddress", debugger_address)

        service = ChromeService(executable_path=driver_path)

        driver = webdriver.Chrome(service=service, options=chrome_options)
        print("✅ [Selenium] 连接成功！浏览器窗口已接管。")

        # 等待浏览器稳定
        time.sleep(5)

        # 获取所有窗口句柄
        handles = driver.window_handles
        print(f"ℹ️  当前共有 {len(handles)} 个标签页")

        # 切换到最新的标签页（通常是最后一个）
        if len(handles) > 1:
            print("🔄 检测到多个标签页，正在切换到最新的标签页...")
            driver.switch_to.window(handles[-1])
            time.sleep(1)
        elif len(handles) == 1:
            driver.switch_to.window(handles[0])
            time.sleep(1)

        # --- 开始自动化操作 ---
        print(f"\n🤖 [自动化] 正在访问: {target_url}")
        driver.get(target_url)

        # 增加初始等待时间，确保页面充分加载
        print("⏳ [自动化] 等待页面加载...")
        time.sleep(10)

        # 打印整个HTML结构用于调试
        print("📄 [调试] 当前页面HTML结构:")
        page_html = driver.page_source
        print(page_html)

        print(f"HTML长度: {len(page_html)} 字符")
        if len(page_html) > 2000:
            print("前1000字符:")
            print(page_html[:1000])
            print("\n后1000字符:")
            print(page_html[-1000:])
        else:
            print(page_html[:2000])
        print("📄 [调试] HTML结构打印完成\n")

        # 等待并检测人机验证元素，最多等待60秒
        print("🔍 [自动化] 正在检测人机验证元素...")
        captcha_element = None
        start_time = time.time()
        while time.time() - start_time < 60:
            try:
                # 直接查找验证码元素
                captcha_elements = driver.find_elements(By.XPATH, '//*[@id="RInW4"]/div/label/input')

                if len(captcha_elements) > 0:
                    captcha_element = captcha_elements[0]
                    print("✅ [自动化] 检测到验证码元素")
                    break
                else:
                    print("⏳ [自动化] 未找到验证码元素，继续等待...")
                    time.sleep(2)  # 等待2秒后再次检查
            except Exception as e:
                print(f"⚠️ [自动化] 检测过程中出现错误: {str(e)[:100]}...")
                time.sleep(2)  # 等待2秒后再次检查

        if captcha_element:
            # 处理人机验证
            try:
                from selenium.webdriver.common.keys import Keys

                # 执行 Tab 键
                print("⌨️  [自动化] 执行 Tab 键...")
                captcha_element.send_keys(Keys.TAB)
                time.sleep(1)

                # 执行空格键
                print("⌨️  [自动化] 执行空格键...")
                captcha_element.send_keys(Keys.SPACE)

                print("⏳ [自动化] 等待验证完成...")
                time.sleep(10)  # 等待验证完成
            except Exception as e:
                print(f"⚠️ [自动化] 人机验证处理过程中出现错误: {e}")
                print("ℹ️ [自动化] 继续执行后续操作...")
        else:
            print("⏰ [自动化] 超时未检测到验证码元素")
            # 即使没有检测到验证码，也尝试查找元素进行操作
            try:
                captcha_elements = driver.find_elements(By.XPATH, '//*[@id="RInW4"]/div/label/input')
                if len(captcha_elements) > 0:
                    print("ℹ️ [自动化] 超时后找到验证码元素，尝试执行操作...")
                    captcha_element = captcha_elements[0]
                    from selenium.webdriver.common.keys import Keys

                    # 执行 Tab 键
                    print("⌨️  [自动化] 执行 Tab 键...")
                    captcha_element.send_keys(Keys.TAB)
                    time.sleep(1)

                    # 执行空格键
                    print("⌨️  [自动化] 执行空格键...")
                    captcha_element.send_keys(Keys.SPACE)

                    print("⏳ [自动化] 等待验证完成...")
                    time.sleep(10)
                else:
                    print("ℹ️ [自动化] 页面上未找到指定的验证码元素")
            except Exception as e:
                print(f"⚠️ [自动化] 超时后尝试操作时出现错误: {e}")

        print(f"   - 当前页面标题: {driver.title}")

        print("✅ [自动化] 操作完成！\n")

        # 等待10秒后关闭浏览器
        print("⏳ 等待10秒后关闭浏览器...")
        time.sleep(10)

    except WebDriverException as e:
        print(f"❌ [自动化] WebDriver错误: {e}")
    except Exception as e:
        print(f"❌ [自动化] 过程中出现严重错误: {e}")

    finally:
        # --- 清理资源 ---
        if driver:
            # 调用 driver.quit() 会关闭由 Selenium 接管的浏览器窗口。
            # MoreLogin 客户端会检测到浏览器关闭并自动停止分析。
            print("🧹 [清理] 任务完成，正在关闭浏览器...")
            try:
                driver.quit()
                print("✅ [清理] 浏览器已关闭。")
            except Exception as e:
                print(f"❌ [清理] 关闭浏览器时出错: {e}")
        else:
            print("⚠️ [清理] 未创建WebDriver实例，无需关闭。")


# 主执行逻辑
def main():
    """主执行函数 - 生产环境"""
    config = Config()

    # 检查配置是否已修改
    if "YOUR_PROFILE_ID" in config.PROFILE_ID:
        print("🚨 [错误] 请先在脚本的 `Config` 类中设置你的 `PROFILE_ID`。")
        return

    if "YOUR_USER_TOKEN" in config.USER_TOKEN:
        print("🚨 [错误] 请先在脚本的 `Config` 类中设置你的 `USER_TOKEN`。")
        return

    # 初始化 API 控制器
    api = MoreLoginAPI(config.BASE_URL)

    # 获取外部授权链接
    auth_url = api.get_external_auth_url()
    if not auth_url:
        print("🚨 [错误] 无法获取外部授权链接，程序终止。")
        return

    # 启动配置文件并获取 WebDriver 连接信息
    profile_data = None
    env_id = None
    # 如果配置了环境ID，则使用新API启动环境
    if hasattr(config, 'ENV_ID') and config.ENV_ID and config.ENV_ID != "YOUR_ENV_ID":
        # 使用新API启动环境
        env_id = config.ENV_ID
        print("ℹ️  使用环境启动方式")

        # 在启动环境前执行指纹刷新和缓存清除操作
        print("\n🔄 执行环境初始化操作...")
        # 刷新指纹
        api.refresh_fingerprint(
            env_id=env_id,
            browser_type=config.BROWSER_TYPE,
            os_type=config.OS_TYPE,
            ua_version=config.UA_VERSION if config.UA_VERSION else None,
            advanced_setting=None
        )

        # 清除本地缓存
        api.remove_local_cache(
            env_id=env_id,
            local_storage=config.CLEAR_LOCAL_STORAGE,
            indexed_db=config.CLEAR_INDEXED_DB,
            cookie=config.CLEAR_COOKIE,
            extension=config.CLEAR_EXTENSION,
            extension_file=config.CLEAR_EXTENSION_FILE
        )

        # 启动环境
        profile_data = api.start_env(
            env_id=config.ENV_ID,
            encrypt_key=getattr(config, 'ENCRYPT_KEY', None),
            is_headless=getattr(config, 'IS_HEADLESS', False),
            cdp_evasion=getattr(config, 'CDP_EVASION', False)
        )
    else:
        # 否则使用旧API启动配置文件
        print("ℹ️  使用配置文件启动方式")
        profile_data = api.start_profile(config.PROFILE_ID)

    if profile_data:
        driver_path, debugger_address = profile_data

        # 执行自动化任务，访问获取到的授权链接
        run_automation_task(driver_path, debugger_address, auth_url)

        # 如果使用了环境启动方式，则在任务完成后调用关闭环境接口
        if env_id:
            print("⏳ 等待10秒后关闭环境...")
            time.sleep(10)
            api.close_env(env_id)
    else:
        print("程序因无法启动浏览器而提前终止。")


if __name__ == "__main__":
    main()