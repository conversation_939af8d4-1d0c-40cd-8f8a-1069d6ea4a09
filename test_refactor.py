#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构验证测试脚本
用于验证重构后的代码是否正常工作
"""

def test_imports():
    """测试模块导入"""
    print("🧪 [测试] 开始测试模块导入...")
    
    try:
        from Config import Config
        print("✅ [测试] Config模块导入成功")
        
        from external_service import ExternalService
        print("✅ [测试] ExternalService模块导入成功")
        
        from browser_automation import BrowserAutomation
        print("✅ [测试] BrowserAutomation模块导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ [测试] 模块导入失败: {e}")
        return False

def test_config():
    """测试配置类"""
    print("\n🧪 [测试] 开始测试配置类...")
    
    try:
        from Config import Config
        config = Config()
        
        # 检查必要的配置项
        assert hasattr(config, 'USER_TOKEN'), "缺少USER_TOKEN配置"
        assert hasattr(config, 'EXTERNAL_SERVICE_URL'), "缺少EXTERNAL_SERVICE_URL配置"
        assert hasattr(config, 'CHROMEDRIVER_PATH'), "缺少CHROMEDRIVER_PATH配置"
        assert hasattr(config, 'HEADLESS_MODE'), "缺少HEADLESS_MODE配置"
        assert hasattr(config, 'CAPTCHA_XPATH'), "缺少CAPTCHA_XPATH配置"
        
        print("✅ [测试] 配置类测试通过")
        return True
    except Exception as e:
        print(f"❌ [测试] 配置类测试失败: {e}")
        return False

def test_external_service():
    """测试外部服务类"""
    print("\n🧪 [测试] 开始测试外部服务类...")
    
    try:
        from external_service import ExternalService
        
        # 创建实例（不进行实际网络请求）
        service = ExternalService("https://example.com", "test_token")
        
        assert hasattr(service, 'get_auth_url'), "缺少get_auth_url方法"
        assert service.base_url == "https://example.com", "base_url设置错误"
        assert service.user_token == "test_token", "user_token设置错误"
        
        print("✅ [测试] 外部服务类测试通过")
        return True
    except Exception as e:
        print(f"❌ [测试] 外部服务类测试失败: {e}")
        return False

def test_browser_automation():
    """测试浏览器自动化类"""
    print("\n🧪 [测试] 开始测试浏览器自动化类...")
    
    try:
        from browser_automation import BrowserAutomation
        
        # 创建实例（不启动实际浏览器）
        browser = BrowserAutomation(chromedriver_path=None, headless=True)
        
        assert hasattr(browser, 'start_browser'), "缺少start_browser方法"
        assert hasattr(browser, 'navigate_to_url'), "缺少navigate_to_url方法"
        assert hasattr(browser, 'handle_captcha'), "缺少handle_captcha方法"
        assert hasattr(browser, 'close_browser'), "缺少close_browser方法"
        
        print("✅ [测试] 浏览器自动化类测试通过")
        return True
    except Exception as e:
        print(f"❌ [测试] 浏览器自动化类测试失败: {e}")
        return False

def test_main_module():
    """测试主模块"""
    print("\n🧪 [测试] 开始测试主模块...")
    
    try:
        import main
        
        assert hasattr(main, 'run_automation_task'), "缺少run_automation_task函数"
        assert hasattr(main, 'main'), "缺少main函数"
        
        print("✅ [测试] 主模块测试通过")
        return True
    except Exception as e:
        print(f"❌ [测试] 主模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 [测试] AutoBrowser重构验证测试开始")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_external_service,
        test_browser_automation,
        test_main_module
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 [测试] 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 [测试] 所有测试通过！重构成功！")
        return True
    else:
        print("❌ [测试] 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    main()
