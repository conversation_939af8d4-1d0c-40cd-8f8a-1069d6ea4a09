import requests
from typing import Optional


class ExternalService:
    """
    外部服务API交互模块
    用于获取OAuth授权链接等外部服务功能
    """

    def __init__(self, base_url: str, user_token: str):
        """
        初始化外部服务客户端
        
        Args:
            base_url: 外部服务基础URL
            user_token: 用户认证令牌
        """
        self.base_url = base_url.rstrip('/')
        self.user_token = user_token

    def get_auth_url(self) -> Optional[str]:
        """
        获取OAuth授权链接
        
        Returns:
            授权链接URL，如果失败则返回 None
        """
        print(f"🚀 [外部服务] 准备获取OAuth授权链接")
        
        try:
            auth_url = f"{self.base_url}/api/external/auth-url"
            headers = {
                "Authorization": f"Bearer {self.user_token}" if self.user_token else ""
            }
            
            print(f"ℹ️  [外部服务] 请求URL: {auth_url}")
            response = requests.get(auth_url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'authUrl' in data:
                        auth_url = data['authUrl']
                        print(f"✅ [外部服务] 成功获取授权链接: {auth_url}")
                        return auth_url
                    else:
                        print(f"❌ [外部服务] 响应中未找到authUrl字段: {data}")
                        return None
                except ValueError as e:
                    print(f"❌ [外部服务] 解析JSON响应失败: {e}")
                    print(f"   响应内容: {response.text}")
                    return None
            else:
                print(f"❌ [外部服务] HTTP请求失败，状态码: {response.status_code}")
                print(f"   响应内容: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print("❌ [外部服务] 请求超时")
            print("   - 👉 请检查网络连接是否正常")
            print("   - 👉 确认外部服务地址是否可访问")
            return None
        except requests.exceptions.ConnectionError:
            print("❌ [外部服务] 连接失败")
            print("   - 👉 请检查外部服务地址是否正确")
            print("   - 👉 确认网络连接是否正常")
            return None
        except requests.exceptions.RequestException as e:
            print(f"❌ [外部服务] 请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ [外部服务] 获取授权链接时发生未知错误: {e}")
            return None
