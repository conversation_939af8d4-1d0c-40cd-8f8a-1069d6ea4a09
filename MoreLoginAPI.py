import requests
from typing import Op<PERSON>, <PERSON><PERSON>

from Config import Config


# MoreLogin API 交互模块
class MoreLoginAPI:
    """
    封装与 MoreLogin 客户端 API 交互的类。
    参考官方文档，使用 /api/v1/browser/start 接口。
    """

    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')  # 确保base_url末尾没有斜杠

    def start_profile(self, profile_id: str) -> Optional[Tuple[str, str]]:
        """
        通过 MoreLogin API 启动指定的浏览器配置文件。

        Args:
            profile_id: 要启动的浏览器配置文件的ID。

        Returns:
            一个元组 (driver_path, debugger_address)，如果失败则返回 None。
        """
        print(f"🚀 [API] 准备启动配置文件: {profile_id}")
        # 根据官方文档，推荐使用 /api/v1/browser/start 接口
        start_url = f"{self.base_url}/api/v1/browser/start"
        params = {"profileId": profile_id}
        headers = {
            "Authorization": f"Bearer {Config.API_TOKEN}" if Config.API_TOKEN else ""
        }

        try:
            response = requests.get(start_url, params=params, headers=headers, timeout=30)
            print(f"   - 请求URL: {response.url}")
            print(f"   - 响应状态码: {response.status_code}")
            
            # 检查 HTTP 请求错误 (如 404, 500)
            response.raise_for_status()
            response_data = response.json()

            # 检查 API 返回的业务状态码
            if response_data.get("code") == 0 and "data" in response_data:
                data = response_data["data"]
                driver_path = data.get("driver")
                debugger_address = data.get("ws")

                # 确保关键信息存在
                if not driver_path or not debugger_address:
                    print("❌ [API] 启动失败: API响应中缺少 'driver' 或 'ws' (debuggerAddress) 字段。")
                    return None

                print("✅ [API] 配置文件启动成功!")
                print(f"   - WebDriver 路径: {driver_path}")
                print(f"   - 调试地址: {debugger_address}")
                return driver_path, debugger_address
            else:
                error_msg = response_data.get('msg', '未知错误')
                print(f"❌ [API] 启动失败: {error_msg}")
                print(f"   - 响应数据: {response_data}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ [API] 请求失败: {e}")
            print("   - 👉 请确保 MoreLogin 客户端正在运行，并且 API 端口正确。")
            print("   - 👉 检查防火墙设置是否阻止了连接。")
            print("   - 👉 确认配置的 API_TOKEN 是否正确。")
            return None
        except Exception as e:
            print(f"❌ [API] 解析响应时发生未知错误: {e}")
            return None

    def start_env(self, env_id: str, encrypt_key: Optional[str] = None, 
                  is_headless: bool = False, cdp_evasion: bool = False) -> Optional[Tuple[str, str]]:
        """
        通过 MoreLogin API 启动指定的环境。

        Args:
            env_id: 要启动的环境ID
            encrypt_key: 密钥，环境开启端对端加密时必传
            is_headless: 是否以 headless 方式启动浏览器环境
            cdp_evasion: 是否启用CDP特征规避机制

        Returns:
            一个元组 (driver_path, debugger_address)，如果失败则返回 None。
        """
        print(f"🚀 [API] 准备启动环境: {env_id}")
        start_url = f"{self.base_url}/api/env/start"
        payload = {
            "envId": env_id,
            "isHeadless": is_headless,
            "cdpEvasion": cdp_evasion
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {Config.API_TOKEN}" if Config.API_TOKEN else ""
        }
        
        # 只有当encrypt_key不为空时才添加到请求参数中
        if encrypt_key:
            payload["encryptKey"] = encrypt_key

        try:
            response = requests.post(start_url, json=payload, headers=headers, timeout=30)
            print(f"   - 请求URL: {start_url}")
            print(f"   - 请求参数: {payload}")
            print(f"   - 响应状态码: {response.status_code}")
            response.raise_for_status()
            response_data = response.json()

            # 检查 API 返回的业务状态码
            if response_data.get("code") == 0 and "data" in response_data:
                data = response_data["data"]
                driver_path = data.get("webdriver")
                debug_port = data.get("debugPort")
                env_id = data.get("envId")

                # 确保关键信息存在
                if not driver_path or not debug_port:
                    print("❌ [API] 启动失败: API响应中缺少 'webdriver' 或 'debugPort' 字段。")
                    return None

                # 构造debugger地址
                debugger_address = f"127.0.0.1:{debug_port}"
                
                print("✅ [API] 环境启动成功!")
                print(f"   - 环境 ID: {env_id}")
                print(f"   - WebDriver 路径: {driver_path}")
                print(f"   - 调试地址: {debugger_address}")
                return driver_path, debugger_address
            else:
                error_msg = response_data.get('msg', '未知错误')
                error_code = response_data.get('code', -1)
                print(f"❌ [API] 启动失败: {error_msg}")
                print(f"   - 响应数据: {response_data}")
                
                # 特殊错误处理
                if error_code == 19128:
                    print("   - 💡 解决方案: 请在 MoreLogin 客户端中为该环境下载对应的浏览器内核")
                    print("   -    步骤1: 打开 MoreLogin 客户端")
                    print("   -    步骤2: 找到环境ID为 " + env_id + " 的环境")
                    print("   -    步骤3: 点击启动环境，客户端会提示下载内核")
                    print("   -    步骤4: 下载完成后再次运行此脚本")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ [API] 请求失败: {e}")
            print("   - 👉 请确保 MoreLogin 客户端正在运行且版本在2.15.0及以上。")
            print("   - 👉 检查防火墙设置是否阻止了连接。")
            print("   - 👉 确认配置的 API_TOKEN 是否正确。")
            return None
        except Exception as e:
            print(f"❌ [API] 解析响应时发生未知错误: {e}")
            return None

    def close_env(self, env_id: str) -> bool:
        """
        通过 MoreLogin API 关闭指定的环境。

        Args:
            env_id: 要关闭的环境ID

        Returns:
            关闭成功返回 True，失败返回 False。
        """
        print(f"🚀 [API] 准备关闭环境: {env_id}")
        close_url = f"{self.base_url}/api/env/close"
        payload = {"envId": env_id}
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {Config.API_TOKEN}" if Config.API_TOKEN else ""
        }

        try:
            response = requests.post(close_url, json=payload, headers=headers, timeout=30)
            print(f"   - 请求URL: {close_url}")
            print(f"   - 请求参数: {payload}")
            print(f"   - 响应状态码: {response.status_code}")
            response.raise_for_status()
            response_data = response.json()

            # 检查 API 返回的业务状态码
            if response_data.get("code") == 0:
                print("✅ [API] 环境关闭成功!")
                return True
            else:
                error_msg = response_data.get('msg', '未知错误')
                print(f"❌ [API] 关闭失败: {error_msg}")
                print(f"   - 响应数据: {response_data}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ [API] 请求失败: {e}")
            print("   - 👉 请确保 MoreLogin 客户端正在运行且版本在2.15.0及以上。")
            print("   - 👉 检查防火墙设置是否阻止了连接。")
            print("   - 👉 确认配置的 API_TOKEN 是否正确。")
            return False
        except Exception as e:
            print(f"❌ [API] 解析响应时发生未知错误: {e}")
            return False

    def remove_local_cache(self, env_id: str, local_storage: bool = False, 
                           indexed_db: bool = False, cookie: bool = False,
                           extension: bool = False, extension_file: bool = False) -> bool:
        """
        清除环境本地缓存。

        Args:
            env_id: 环境ID
            local_storage: 是否清除 LocalStorage
            indexed_db: 是否清除 IndexedDB
            cookie: 是否清除 cookie
            extension: 是否清除扩展
            extension_file: 是否清除扩展文件

        Returns:
            清除成功返回 True，失败返回 False。
        """
        print(f"🚀 [API] 准备清除环境 {env_id} 的本地缓存")
        url = f"{self.base_url}/api/env/removeLocalCache"
        payload = {
            "envId": env_id,
            "localStorage": local_storage,
            "indexedDB": indexed_db,
            "cookie": cookie,
            "extension": extension,
            "extensionFile": extension_file
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {Config.API_TOKEN}" if Config.API_TOKEN else ""
        }

        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            print(f"   - 请求URL: {url}")
            print(f"   - 请求参数: {payload}")
            print(f"   - 响应状态码: {response.status_code}")
            response.raise_for_status()
            response_data = response.json()

            if response_data.get("code") == 0:
                print("✅ [API] 本地缓存清除成功!")
                return True
            else:
                error_msg = response_data.get('msg', '未知错误')
                print(f"❌ [API] 本地缓存清除失败: {error_msg}")
                print(f"   - 响应数据: {response_data}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ [API] 请求失败: {e}")
            print("   - 👉 请确保 MoreLogin 客户端正在运行且版本在2.28.0及以上。")
            print("   - 👉 检查防火墙设置是否阻止了连接。")
            print("   - 👉 确认配置的 API_TOKEN 是否正确。")
            return False
        except Exception as e:
            print(f"❌ [API] 解析响应时发生未知错误: {e}")
            return False

    def refresh_fingerprint(self, env_id: str, browser_type: int, os_type: int, 
                            ua_version: Optional[int] = None, advanced_setting: Optional[dict] = None) -> bool:
        """
        刷新指纹。

        Args:
            env_id: 环境ID
            browser_type: 浏览器类型 (1:Chrome, 2:Firefox)
            os_type: 操作系统类型 (1:Windows, 2:macOS, 3:Android, 4:IOS)
            ua_version: UA 版本
            advanced_setting: 高级配置

        Returns:
            刷新成功返回 True，失败返回 False。
        """
        print(f"🚀 [API] 准备刷新环境 {env_id} 的指纹")
        url = f"{self.base_url}/api/env/fingerprint/refresh"
        payload = {
            "envId": env_id,
            "browserTypeld": browser_type,
            "operatorSystemld": os_type
        }
        
        if ua_version is not None:
            payload["uaVersion"] = ua_version
            
        if advanced_setting is not None:
            payload["advancedSetting"] = advanced_setting
            
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {Config.API_TOKEN}" if Config.API_TOKEN else ""
        }

        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            print(f"   - 请求URL: {url}")
            print(f"   - 请求参数: {payload}")
            print(f"   - 响应状态码: {response.status_code}")
            response.raise_for_status()
            response_data = response.json()

            if response_data.get("code") == 0:
                print("✅ [API] 指纹刷新成功!")
                return True
            else:
                error_msg = response_data.get('msg', '未知错误')
                print(f"❌ [API] 指纹刷新失败: {error_msg}")
                print(f"   - 响应数据: {response_data}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ [API] 请求失败: {e}")
            print("   - 👉 请确保 MoreLogin 客户端正在运行且版本在2.28.0及以上。")
            print("   - 👉 检查防火墙设置是否阻止了连接。")
            print("   - 👉 确认配置的 API_TOKEN 是否正确。")
            return False
        except Exception as e:
            print(f"❌ [API] 解析响应时发生未知错误: {e}")
            return False

    def get_external_auth_url(self) -> Optional[str]:
        """
        通过外部服务获取OAuth授权链接。

        Returns:
            授权链接URL，如果失败则返回 None。
        """
        print(f"🚀 [API] 准备获取外部服务授权链接")
        auth_url = f"{Config.EXTERNAL_SERVICE_URL}/api/external/auth-url"
        headers = {
            "Authorization": f"Bearer {Config.USER_TOKEN}" if Config.USER_TOKEN else ""
        }

        try:
            response = requests.get(auth_url, headers=headers, timeout=30)
            print(f"   - 请求URL: {auth_url}")
            print(f"   - 响应状态码: {response.status_code}")
            response.raise_for_status()
            response_data = response.json()

            if "authorize_url" in response_data:
                authorize_url = response_data["authorize_url"]
                print("✅ [API] 成功获取授权链接!")
                print(f"   - 授权链接: {authorize_url}")
                return authorize_url
            else:
                print("❌ [API] 获取授权链接失败: 响应中缺少 'authorize_url' 字段")
                print(f"   - 响应数据: {response_data}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ [API] 请求失败: {e}")
            print("   - 👉 请确保网络连接正常")
            print("   - 👉 检查防火墙设置是否阻止了连接")
            print("   - 👉 确认配置的 USER_TOKEN 是否正确")
            return None
        except Exception as e:
            print(f"❌ [API] 解析响应时发生未知错误: {e}")
            return None

    # MoreLogin 不需要手动调用 stop 接口，关闭由 WebDriver 控制的浏览器即可自动停止
    # 因此，stop_profile 函数可以移除，以简化代码并遵循最佳实践。