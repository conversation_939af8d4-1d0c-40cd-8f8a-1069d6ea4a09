# AutoBrowser - 本地浏览器自动化工具

## 项目简介

AutoBrowser 是一个基于 Selenium 的本地浏览器自动化工具，专门用于处理 OAuth 授权流程和人机验证。该工具使用本地 Chrome 浏览器，无需依赖第三方浏览器管理服务。

## 主要功能

- 🚀 **本地浏览器**: 使用本地 Chrome 浏览器进行自动化操作
- 🤖 **自动化操作**: 自动处理 OAuth 授权和人机验证
- 🔗 **外部服务集成**: 支持获取外部 OAuth 授权链接
- 🛡️ **反检测特性**: 内置反自动化检测机制
- ⚙️ **灵活配置**: 支持无头模式、自定义等待时间等配置

## 技术栈

- **Python 3.11+**: 主要开发语言
- **Selenium WebDriver**: 浏览器自动化框架
- **Chrome/Chromium**: 本地浏览器
- **Requests**: HTTP 请求处理（外部服务API调用）

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd AutoBrowser

# 创建虚拟环境（推荐）
python -m venv .venv
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/macOS

# 安装依赖
pip install -r requirements.txt
```

### 2. 浏览器环境准备

确保系统已安装：
- **Chrome 浏览器**: 最新版本
- **ChromeDriver**: 与 Chrome 版本匹配的 ChromeDriver
  - 下载地址: https://chromedriver.chromium.org/
  - 将 chromedriver.exe 添加到系统 PATH 或指定具体路径

### 3. 配置设置

编辑 `Config.py` 文件，设置必要的配置项：

```python
class Config:
    # 外部服务配置
    USER_TOKEN = "your_user_token_here"  # 替换为实际的用户TOKEN
    EXTERNAL_SERVICE_URL = "https://your-service-url.com"

    # 本地浏览器配置
    CHROMEDRIVER_PATH = None  # 或指定具体路径，如 "C:/chromedriver/chromedriver.exe"
    HEADLESS_MODE = False     # 是否无头模式运行

    # 其他配置...
```

### 4. 运行程序

```bash
# 激活虚拟环境
.venv\Scripts\activate

# 运行主程序
python main.py
```

## 项目结构

```
AutoBrowser/
├── main.py                 # 主程序入口
├── Config.py              # 配置管理
├── browser_automation.py  # 浏览器自动化模块
├── external_service.py    # 外部服务API模块
├── requirements.txt       # 项目依赖
├── .augment/              # Augment配置目录
│   └── rules/
│       └── project.md     # 项目规则配置
└── README.md             # 项目文档
```

## 配置说明

### 外部服务配置

- `USER_TOKEN`: 外部服务用户认证令牌
- `EXTERNAL_SERVICE_URL`: 外部服务API基础地址

### 本地浏览器配置

- `CHROMEDRIVER_PATH`: ChromeDriver可执行文件路径（None使用系统PATH）
- `HEADLESS_MODE`: 是否以无头模式运行浏览器
- `BROWSER_STARTUP_WAIT`: 浏览器启动等待时间（秒）
- `PAGE_LOAD_WAIT`: 页面加载等待时间（秒）

### 人机验证配置

- `CAPTCHA_XPATH`: 验证码元素的XPath定位器
- `CAPTCHA_TIMEOUT`: 验证码检测超时时间（秒）

### 调试配置

- `DEBUG_MODE`: 是否启用调试模式
- `OUTPUT_PAGE_SOURCE`: 是否输出页面HTML源码

## 使用示例

### 基本使用

```python
from Config import Config
from browser_automation import BrowserAutomation
from external_service import ExternalService

# 初始化配置
config = Config()

# 获取外部授权链接
external_service = ExternalService(
    base_url=config.EXTERNAL_SERVICE_URL,
    user_token=config.USER_TOKEN
)
auth_url = external_service.get_auth_url()

# 启动浏览器自动化
browser = BrowserAutomation(
    chromedriver_path=config.CHROMEDRIVER_PATH,
    headless=config.HEADLESS_MODE
)

if browser.start_browser():
    browser.navigate_to_url(auth_url)
    browser.handle_captcha()
    browser.close_browser()
```

### 自定义自动化任务

```python
from browser_automation import BrowserAutomation

def custom_automation_task(config, target_url):
    """自定义自动化任务"""
    browser = BrowserAutomation(
        chromedriver_path=config.CHROMEDRIVER_PATH,
        headless=config.HEADLESS_MODE
    )

    try:
        if browser.start_browser():
            browser.navigate_to_url(target_url)
            # 执行自定义操作
            # ... 其他操作
    finally:
        browser.close_browser()
```

## 开发规范

详细的开发规范和代码风格请参考 [.augment/rules/project.md](.augment/rules/project.md) 文件。

### 关键规范

- 使用中文注释和文档
- 错误信息包含 emoji 图标
- 函数使用 snake_case 命名
- 类使用 PascalCase 命名
- 完善的错误处理和资源清理

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查 Chrome 浏览器是否已安装
   - 确认 ChromeDriver 版本与 Chrome 版本匹配
   - 验证 ChromeDriver 是否在系统 PATH 中或路径配置正确

2. **外部服务连接失败**
   - 检查网络连接是否正常
   - 确认 USER_TOKEN 是否正确
   - 验证 EXTERNAL_SERVICE_URL 是否可访问

3. **人机验证处理失败**
   - 检查验证码元素 XPath 是否正确
   - 增加页面加载等待时间
   - 确认目标网站的验证码元素结构

4. **ChromeDriver 相关问题**
   - 下载与 Chrome 版本匹配的 ChromeDriver
   - 将 ChromeDriver 添加到系统 PATH
   - 或在配置中指定 ChromeDriver 的完整路径

## 版本要求

- Python 3.11+
- Chrome/Chromium 浏览器（最新版本）
- ChromeDriver（与浏览器版本匹配）
- Selenium 4.15.0+

## 许可证

本项目采用 MIT 许可证。详情请参阅 LICENSE 文件。

## 贡献指南

欢迎提交 Issue 和 Pull Request。在贡献代码前，请确保：

1. 遵循项目的代码规范
2. 添加适当的测试用例
3. 更新相关文档
4. 确保所有测试通过

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**最后更新**: 2025-08-07  
**版本**: 1.0.0
