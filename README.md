# AutoBrowser - 浏览器自动化工具

## 项目简介

AutoBrowser 是一个基于 Selenium 和 MoreLogin 的浏览器自动化工具，专门用于处理 OAuth 授权流程和人机验证。该工具支持多种浏览器环境管理，包括指纹刷新、缓存清理等高级功能。

## 主要功能

- 🚀 **MoreLogin 集成**: 支持通过 MoreLogin API 管理浏览器环境
- 🤖 **自动化操作**: 自动处理 OAuth 授权和人机验证
- 🔄 **环境管理**: 支持指纹刷新和本地缓存清理
- 🛡️ **安全特性**: 支持端对端加密和 CDP 特征规避
- 🧪 **测试模式**: 支持不使用 MoreLogin 的本地测试环境

## 技术栈

- **Python 3.x**: 主要开发语言
- **Selenium WebDriver**: 浏览器自动化框架
- **MoreLogin API**: 浏览器环境管理
- **Requests**: HTTP 请求处理

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd AutoBrowser

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

编辑 `Config.py` 文件，设置必要的配置项：

```python
class Config:
    # MoreLogin API 配置
    API_TOKEN = "your_api_token_here"
    PROFILE_ID = "your_profile_id_here"
    ENV_ID = "your_env_id_here"
    
    # 外部服务配置
    USER_TOKEN = "your_user_token_here"
    
    # 其他配置...
```

### 3. 运行程序

```bash
# 生产环境模式（使用 MoreLogin）
python main.py

# 测试环境模式（不使用 MoreLogin）
python test_main.py
```

## 项目结构

```
AutoBrowser/
├── main.py              # 主程序入口（生产环境）
├── test_main.py         # 测试程序入口（测试环境）
├── Config.py            # 配置管理
├── MoreLoginAPI.py      # MoreLogin API 封装
├── requirements.txt     # 项目依赖
├── .augment-rules.md    # Augment 项目规则配置
└── README.md           # 项目文档
```

## 配置说明

### MoreLogin 配置

- `API_TOKEN`: MoreLogin 客户端 API 令牌
- `PROFILE_ID`: 浏览器配置文件 ID
- `ENV_ID`: 环境 ID（优先使用环境启动方式）
- `BASE_URL`: MoreLogin API 基础地址（默认: http://127.0.0.1:40000）

### 浏览器配置

- `BROWSER_TYPE`: 浏览器类型（1:Chrome, 2:Firefox）
- `OS_TYPE`: 操作系统类型（1:Windows, 2:macOS, 3:Android, 4:IOS）
- `UA_VERSION`: User Agent 版本号

### 缓存清理配置

- `CLEAR_LOCAL_STORAGE`: 是否清除 LocalStorage
- `CLEAR_INDEXED_DB`: 是否清除 IndexedDB
- `CLEAR_COOKIE`: 是否清除 Cookie
- `CLEAR_EXTENSION`: 是否清除扩展
- `CLEAR_EXTENSION_FILE`: 是否清除扩展文件

## 使用示例

### 基本使用

```python
from Config import Config
from MoreLoginAPI import MoreLoginAPI

# 初始化配置和 API
config = Config()
api = MoreLoginAPI(config.BASE_URL)

# 启动环境
profile_data = api.start_env(config.ENV_ID)
if profile_data:
    driver_path, debugger_address = profile_data
    # 执行自动化任务...
```

### 自定义自动化任务

```python
def custom_automation_task(driver_path, debugger_address, target_url):
    """自定义自动化任务"""
    # 连接到浏览器
    chrome_options = ChromeOptions()
    chrome_options.add_experimental_option("debuggerAddress", debugger_address)
    
    driver = webdriver.Chrome(service=ChromeService(driver_path), options=chrome_options)
    
    try:
        # 执行自定义操作
        driver.get(target_url)
        # ... 其他操作
    finally:
        driver.quit()
```

## 开发规范

详细的开发规范和代码风格请参考 [.augment-rules.md](.augment-rules.md) 文件。

### 关键规范

- 使用中文注释和文档
- 错误信息包含 emoji 图标
- 函数使用 snake_case 命名
- 类使用 PascalCase 命名
- 完善的错误处理和资源清理

## 故障排除

### 常见问题

1. **MoreLogin 连接失败**
   - 确保 MoreLogin 客户端正在运行
   - 检查 API_TOKEN 是否正确
   - 验证防火墙设置

2. **浏览器启动失败**
   - 确保已下载对应的浏览器内核
   - 检查环境 ID 是否有效
   - 验证 ChromeDriver 版本兼容性

3. **人机验证处理失败**
   - 检查页面元素 XPath 是否正确
   - 增加页面加载等待时间
   - 验证验证码元素是否存在

## 版本要求

- Python 3.7+
- MoreLogin 客户端 2.15.0+
- Chrome/Chromium 浏览器
- ChromeDriver（与浏览器版本匹配）

## 许可证

本项目采用 MIT 许可证。详情请参阅 LICENSE 文件。

## 贡献指南

欢迎提交 Issue 和 Pull Request。在贡献代码前，请确保：

1. 遵循项目的代码规范
2. 添加适当的测试用例
3. 更新相关文档
4. 确保所有测试通过

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**最后更新**: 2025-08-07  
**版本**: 1.0.0
